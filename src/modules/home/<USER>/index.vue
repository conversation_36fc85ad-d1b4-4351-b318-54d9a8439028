<template>
  <ly-layout class="min-h-screen">
    <div class="px-4 bg-white">
      <uni-search-bar v-model="searchValue" placeholder="请输入药材名称关键词" @confirm="handleSearch" @clear="handleClear" cancelButton="none" />
    </div>
    <div class="flex-1 px-4 pt-2 pr-5 box-border bg-gradient-to-br from-gray-50 to-blue-50">
      <van-index-bar :index-list="indexList">
        <view v-for="item in listLetter" :key="item.letter">
          <van-index-anchor :index="item.letter" />
          <div class="grid grid-cols-4 gap-3 my-2">
            <div v-for="item in item.data" :key="item.id" @click="handleItemClick(item)" class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col h-16">
              <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
              <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
            </div>
          </div>
        </view>
      </van-index-bar>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicinalItem } from '@/monkey/types';

  const list = ref<MedicinalItem[]>([]);

  // 药材列表
  const listLetter = ref<MedicinalItem[]>([]);

  // 索引列表
  const indexList = ref<string[]>([]);

  // 搜索值
  const searchValue = ref<string>('');

  /**
   * 从ywsc字段中提取拼音
   * @param ywsc ywsc字段内容，格式如：鸡内金,jnj,鸡黄皮,鸡盹皮
   * @returns 提取的拼音字符串
   */
  const extractPinyinFromYwsc = (ywsc: string) => {
    if (!ywsc) return '';

    const ywscParts = ywsc.split(',');

    // 尝试找到拼音缩写（通常是纯小写字母的部分）
    for (const part of ywscParts) {
      const trimmedPart = part.trim();
      // 检查是否是拼音缩写（纯小写字母）
      if (/^[a-z]+$/.test(trimmedPart)) {
        return trimmedPart;
      }
    }

    return '';
  };

  /**
   * 获取药材列表
   */
  const getMedicinalItemList = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemList();
      if (errcode == 0) {
        console.log('🚀 ~ getMedicinalItemList ~ data:', data);
        // 将数据转换为MedicinalItem类型，并提取拼音字段
        const enrichedData = data.map((item: MedicinalItem) => ({
          ...item,
          pinyin: extractPinyinFromYwsc(item.ywsc), // 从ywsc字段提取拼音
        }));

        list.value = enrichedData;
        // 将数据转换为索引列表
        listLetter.value = createLetterList(enrichedData);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 创建索引列表
   * @param data 药材列表
   * @returns
   */
  const createLetterList = (data: MedicinalItem[]) => {
    // 按首字母分组
    const groupedData = {};

    data.forEach((item) => {
      // 从ywsc字段中提取拼音缩写
      const ywscParts = item.ywsc.split(',');

      // 尝试找到拼音缩写（通常是纯小写字母的部分）
      let pinyin = '';
      for (const part of ywscParts) {
        // 检查是否是拼音缩写（纯小写字母）
        if (/^[a-z]+$/.test(part.trim())) {
          pinyin = part.trim();
          break;
        }
      }

      // 如果没找到拼音，使用药材名称的首字母
      const firstLetter = pinyin.charAt(0).toUpperCase() || item.bzmc.charAt(0).toUpperCase();

      // 如果该首字母组不存在，则创建
      if (!groupedData[firstLetter]) {
        groupedData[firstLetter] = [];
      }

      // 将药材添加到对应首字母组，并添加提取的拼音
      const enrichedItem = {
        ...item,
        pinyin: pinyin, // 添加提取出的拼音
      };

      groupedData[firstLetter].push(enrichedItem);
    });

    // 转换为list格式
    const formattedList = Object.keys(groupedData)
      .sort()
      .map((letter) => ({
        letter,
        data: groupedData[letter],
      }));

    indexList.value = formattedList.map((item) => item.letter);
    return formattedList;
  };

  /**
   * 过滤列表
   * @param list 药材列表
   * @returns
   */
  const filterList = (list: MedicinalItem[]) => {
    const searchTerm = searchValue.value.toLowerCase().trim();

    if (!searchTerm) {
      return list;
    }

    return list.filter((item) => {
      // 匹配药材名称
      const nameMatch = item.bzmc.toLowerCase().includes(searchTerm);

      // 匹配拼音（如果存在）
      const pinyinMatch = item.pinyin && item.pinyin.toLowerCase().includes(searchTerm);

      // 从ywsc字段中提取完整拼音进行匹配
      let ywscPinyinMatch = false;
      let pinyinInitialMatch = false;

      if (item.ywsc) {
        const ywscParts = item.ywsc.split(',');

        ywscParts.forEach((part) => {
          const trimmedPart = part.trim().toLowerCase();

          // 检查是否是拼音（纯字母）
          if (/^[a-z]+$/.test(trimmedPart)) {
            // 完整拼音匹配
            if (trimmedPart.includes(searchTerm)) {
              ywscPinyinMatch = true;
            }

            // 拼音首字母缩写匹配（例如：baishao -> bs）
            if (trimmedPart.length > 1) {
              // 尝试按常见的拼音分割方式提取首字母
              const initials = extractPinyinInitials(trimmedPart);
              if (initials.includes(searchTerm)) {
                pinyinInitialMatch = true;
              }
            }
          }
        });
      }

      // 如果item.pinyin存在，也检查其首字母缩写
      if (item.pinyin && !pinyinInitialMatch) {
        const initials = extractPinyinInitials(item.pinyin.toLowerCase());
        pinyinInitialMatch = initials.includes(searchTerm);
      }

      return nameMatch || pinyinMatch || pinyinInitialMatch || ywscPinyinMatch;
    });
  };

  /**
   * 提取拼音首字母缩写
   * @param pinyin 拼音字符串
   * @returns 首字母缩写
   */
  const extractPinyinInitials = (pinyin: string) => {
    // 常见的拼音分割模式
    const patterns = [
      // 双字拼音模式：baishao -> bs
      /^([a-z]{2,})([a-z]{2,})$/,
      // 三字拼音模式：chuanxiong -> cx (取首尾), cxx (取每个音节首字母)
      /^([a-z]{2,})([a-z]{2,})([a-z]{2,})$/,
      // 更复杂的模式可以根据需要添加
    ];

    const results = [];

    // 尝试双字模式
    const doubleMatch = pinyin.match(/^([a-z]{2,})([a-z]{2,})$/);
    if (doubleMatch) {
      const [, first, second] = doubleMatch;
      results.push(first.charAt(0) + second.charAt(0)); // bs
    }

    // 尝试三字模式
    const tripleMatch = pinyin.match(/^([a-z]{2,})([a-z]{2,})([a-z]{2,})$/);
    if (tripleMatch) {
      const [, first, second, third] = tripleMatch;
      results.push(first.charAt(0) + second.charAt(0) + third.charAt(0)); // 每个音节首字母
      results.push(first.charAt(0) + third.charAt(0)); // 首尾字母
    }

    // 简单的首字母提取（适用于较短的拼音）
    if (pinyin.length >= 2 && pinyin.length <= 8) {
      // 尝试按2个字母一组分割
      for (let i = 0; i < pinyin.length - 1; i += 2) {
        if (i + 1 < pinyin.length) {
          const syllable = pinyin.substring(i, i + 2);
          if (syllable.length === 2) {
            results.push(syllable.charAt(0));
          }
        }
      }

      // 如果是偶数长度，尝试提取首字母组合
      if (pinyin.length % 2 === 0) {
        let initials = '';
        for (let i = 0; i < pinyin.length; i += 2) {
          initials += pinyin.charAt(i);
        }
        if (initials.length > 1) {
          results.push(initials);
        }
      }
    }

    return [...new Set(results)]; // 去重
  };

  /**
   * 搜索
   */
  const handleSearch = () => {
    console.log('🚀 ~ handleSearch ~ searchValue:', searchValue.value);
    const result = filterList(list.value);
    if (result.length > 0) {
      // 过滤列表
      listLetter.value = createLetterList(result);
    } else {
      monkey.$helper.toast.warning('暂无数据');
    }
  };

  /**
   * 清空搜索
   */
  const handleClear = () => {
    listLetter.value = createLetterList(list.value);
  };

  /**
   * 点击药材项
   * @param item 药材项
   */
  const handleItemClick = (item: MedicinalItem) => {
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?ycbm=${item.ycbm}`);
  };

  onLoad(() => {
    getMedicinalItemList();
  });
</script>

<style lang="scss" scoped>
</style>
