<template>
  <ly-layout class="min-h-screen">
    <div class="px-4 bg-white">
      <uni-search-bar v-model="searchValue" placeholder="请输入药材名称关键词" @confirm="handleSearch" @clear="handleClear" cancelButton="none" />
    </div>
    <div class="flex-1 px-4 pt-2 pr-5 box-border bg-gradient-to-br from-gray-50 to-blue-50">
      <van-index-bar :index-list="indexList">
        <view v-for="item in listLetter" :key="item.letter">
          <van-index-anchor :index="item.letter" />
          <div class="grid grid-cols-4 gap-3 my-2">
            <div v-for="item in item.data" :key="item.id" @click="handleItemClick(item)" class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col h-16">
              <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
              <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
            </div>
          </div>
        </view>
      </van-index-bar>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicinalItem } from '@/monkey/types';

  const list = ref<MedicinalItem[]>([]);

  // 药材列表
  const listLetter = ref<MedicinalItem[]>([]);

  // 索引列表
  const indexList = ref<string[]>([]);

  // 搜索值
  const searchValue = ref<string>('');

  /**
   * 从ywsc字段中提取拼音
   * @param ywsc ywsc字段内容，格式如：鸡内金,jnj,鸡黄皮,鸡盹皮
   * @returns 提取的拼音字符串
   */
  const extractPinyinFromYwsc = (ywsc: string) => {
    if (!ywsc) return '';

    const ywscParts = ywsc.split(',');

    // 尝试找到拼音缩写（通常是纯小写字母的部分）
    for (const part of ywscParts) {
      const trimmedPart = part.trim();
      // 检查是否是拼音缩写（纯小写字母）
      if (/^[a-z]+$/.test(trimmedPart)) {
        return trimmedPart;
      }
    }

    return '';
  };

  /**
   * 获取药材列表
   */
  const getMedicinalItemList = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemList();
      if (errcode == 0) {
        console.log('🚀 ~ getMedicinalItemList ~ data:', data);
        // 将数据转换为MedicinalItem类型，并提取拼音字段
        const enrichedData = data.map((item: MedicinalItem) => ({
          ...item,
          pinyin: extractPinyinFromYwsc(item.ywsc), // 从ywsc字段提取拼音
        }));

        list.value = enrichedData;
        console.log("🚀 ~ getMedicinalItemList ~ enrichedData:", enrichedData)
        // 将数据转换为索引列表
        listLetter.value = createLetterList(enrichedData);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 创建索引列表
   * @param data 药材列表
   * @returns
   */
  const createLetterList = (data: MedicinalItem[]) => {
    // 按首字母分组
    const groupedData = {};

    data.forEach((item) => {
      // 使用已提取的拼音或药材名称的首字母
      const firstLetter = (item.pinyin && item.pinyin.charAt(0).toUpperCase()) || item.bzmc.charAt(0).toUpperCase();

      // 如果该首字母组不存在，则创建
      if (!groupedData[firstLetter]) {
        groupedData[firstLetter] = [];
      }

      // 将药材添加到对应首字母组
      groupedData[firstLetter].push(item);
    });

    // 转换为list格式
    const formattedList = Object.keys(groupedData)
      .sort()
      .map((letter) => ({
        letter,
        data: groupedData[letter],
      }));

    indexList.value = formattedList.map((item) => item.letter);
    return formattedList;
  };

  /**
   * 过滤列表
   * @param list 药材列表
   * @returns
   */
  const filterList = (list: MedicinalItem[]) => {
    const searchTerm = searchValue.value.toLowerCase().trim();

    if (!searchTerm) {
      return list;
    }

    return list.filter((item) => {
      // 匹配药材名称
      const nameMatch = item.bzmc.toLowerCase().includes(searchTerm);

      // 匹配拼音（如果存在）
      const pinyinMatch = item.pinyin && item.pinyin.toLowerCase().includes(searchTerm);

      // 拼音首字母缩写匹配
      let pinyinInitialMatch = false;
      if (item.pinyin) {
        const initials = extractPinyinInitials(item.pinyin.toLowerCase());
        console.log(`🚀 拼音: ${item.pinyin}, 提取的首字母: ${JSON.stringify(initials)}, 搜索词: ${searchTerm}`);
        pinyinInitialMatch = initials.includes(searchTerm);
      }

      // 从ywsc字段中查找其他可能的拼音匹配（作为备用）
      let ywscPinyinMatch = false;
      if (item.ywsc && !pinyinMatch && !pinyinInitialMatch) {
        const ywscParts = item.ywsc.split(',');
        ywscParts.forEach((part) => {
          const trimmedPart = part.trim().toLowerCase();
          // 检查是否是拼音（纯字母）且不同于已提取的拼音
          if (/^[a-z]+$/.test(trimmedPart) && trimmedPart !== item.pinyin) {
            if (trimmedPart.includes(searchTerm)) {
              ywscPinyinMatch = true;
            }
          }
        });
      }

      const result = nameMatch || pinyinMatch || pinyinInitialMatch || ywscPinyinMatch;
      if (result) {
        console.log(`✅ 匹配成功: ${item.bzmc}, 名称匹配: ${nameMatch}, 拼音匹配: ${pinyinMatch}, 首字母匹配: ${pinyinInitialMatch}, ywsc匹配: ${ywscPinyinMatch}`);
      }

      return result;
    });
  };

  /**
   * 提取拼音首字母缩写
   * @param pinyin 拼音字符串
   * @returns 首字母缩写数组
   */
  const extractPinyinInitials = (pinyin: string) => {
    if (!pinyin || pinyin.length < 2) return [];

    const results = [];

    // 常见的中药拼音模式匹配
    const commonPatterns = [
      // 双音节模式：baishao(白芍) -> bs, gancao(甘草) -> gc
      { pattern: /^([a-z]{2,4})([a-z]{2,4})$/, extract: (match) => [match[1][0] + match[2][0]] },

      // 三音节模式：chuanxiong(川芎) -> cx, cxx
      { pattern: /^([a-z]{2,4})([a-z]{2,4})([a-z]{2,4})$/, extract: (match) => [
        match[1][0] + match[2][0] + match[3][0], // 每个音节首字母
        match[1][0] + match[3][0] // 首尾字母
      ]},

      // 四音节模式：较少见，但也支持
      { pattern: /^([a-z]{2,3})([a-z]{2,3})([a-z]{2,3})([a-z]{2,3})$/, extract: (match) => [
        match[1][0] + match[2][0] + match[3][0] + match[4][0], // 每个音节首字母
        match[1][0] + match[4][0] // 首尾字母
      ]}
    ];

    // 尝试匹配常见模式
    for (const { pattern, extract } of commonPatterns) {
      const match = pinyin.match(pattern);
      if (match) {
        results.push(...extract(match));
        break; // 找到匹配就停止
      }
    }

    // 如果没有匹配到常见模式，使用简单的分割方法
    if (results.length === 0) {
      // 按长度进行简单分割
      if (pinyin.length >= 4 && pinyin.length <= 8) {
        // 尝试按2-4个字母一组分割
        const syllableLength = Math.floor(pinyin.length / 2);
        if (syllableLength >= 2) {
          const firstSyllable = pinyin.substring(0, syllableLength);
          const secondSyllable = pinyin.substring(syllableLength);
          if (firstSyllable.length >= 2 && secondSyllable.length >= 2) {
            results.push(firstSyllable[0] + secondSyllable[0]);
          }
        }
      }

      // 备用方案：每2个字母取首字母
      if (results.length === 0 && pinyin.length >= 4) {
        let initials = '';
        for (let i = 0; i < pinyin.length; i += 2) {
          if (i < pinyin.length) {
            initials += pinyin[i];
          }
        }
        if (initials.length >= 2) {
          results.push(initials);
        }
      }
    }

    return [...new Set(results)]; // 去重
  };

  /**
   * 搜索
   */
  const handleSearch = () => {
    console.log('🚀 ~ handleSearch ~ searchValue:', searchValue.value);
    const result = filterList(list.value);
    if (result.length > 0) {
      // 过滤列表
      listLetter.value = createLetterList(result);
    } else {
      monkey.$helper.toast.warning('暂无数据');
    }
  };

  /**
   * 清空搜索
   */
  const handleClear = () => {
    listLetter.value = createLetterList(list.value);
  };

  /**
   * 点击药材项
   * @param item 药材项
   */
  const handleItemClick = (item: MedicinalItem) => {
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?ycbm=${item.ycbm}`);
  };

  onLoad(() => {
    getMedicinalItemList();
  });
</script>

<style lang="scss" scoped>
</style>
