<template>
  <ly-layout class="min-h-screen">
    <div class="px-4 bg-white">
      <uni-search-bar v-model="searchValue" placeholder="请输入药材名称或拼音" @confirm="handleSearch" @clear="handleClear" cancelButton="none" />
    </div>
    <div class="flex-1 px-4 pt-2 pr-5 box-border bg-gradient-to-br from-gray-50 to-blue-50">
      <van-index-bar :index-list="indexList">
        <view v-for="item in listLetter" :key="item.letter">
          <van-index-anchor :index="item.letter" />
          <div class="grid grid-cols-4 gap-3 my-2">
            <div v-for="item in item.data" :key="item.id" @click="handleItemClick(item)" class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col h-16">
              <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
              <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
            </div>
          </div>
        </view>
      </van-index-bar>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicinalItem } from '@/monkey/types';

  const list = ref<MedicinalItem[]>([]);

  // 药材列表
  const listLetter = ref<MedicinalItem[]>([]);

  // 索引列表
  const indexList = ref<string[]>([]);

  // 搜索值
  const searchValue = ref<string>('');

  /**
   * 将拼音字符串分割成音节数组
   * @param pinyin 拼音字符串，如 "baishao"
   * @returns 音节数组，如 ["bai", "shao"]
   */
  const splitPinyinToSyllables = (pinyin: string) => {
    if (!pinyin) return [];

    // 常见的中药拼音音节模式
    const syllablePatterns = [
      // 常见的双字母音节
      'ai', 'ao', 'an', 'ang', 'ei', 'en', 'eng', 'er', 'ou', 'uo', 'ua', 'ui', 'un', 'ue', 'uan', 'uang', 'uai', 'ong', 'iong', 'iang', 'ing', 'ian', 'iao', 'ie', 'in', 'iu',
      // 三字母音节
      'ang', 'eng', 'ing', 'ong', 'uan', 'uai', 'uang', 'iang', 'iong', 'iao', 'ian'
    ];

    const syllables = [];
    let remaining = pinyin.toLowerCase();

    while (remaining.length > 0) {
      let matched = false;

      // 尝试匹配较长的音节模式（从长到短）
      for (let len = Math.min(4, remaining.length); len >= 2; len--) {
        const candidate = remaining.substring(0, len);

        // 检查是否是常见音节结尾
        if (syllablePatterns.some(pattern => candidate.endsWith(pattern)) ||
            len === remaining.length) { // 如果是最后一段，直接接受
          syllables.push(candidate);
          remaining = remaining.substring(len);
          matched = true;
          break;
        }
      }

      // 如果没有匹配到，按最小单位处理
      if (!matched) {
        if (remaining.length >= 2) {
          syllables.push(remaining.substring(0, 2));
          remaining = remaining.substring(2);
        } else {
          syllables.push(remaining);
          remaining = '';
        }
      }
    }

    return syllables;
  };

  /**
   * 从ywsc字段中提取拼音并转换为音节数组
   * @param ywsc ywsc字段内容，格式如：鸡内金,jnj,鸡黄皮,鸡盹皮
   * @returns 拼音信息对象
   */
  const extractPinyinFromYwsc = (ywsc: string) => {
    if (!ywsc) return { original: '', syllables: [], initials: '' };

    const ywscParts = ywsc.split(',');

    // 尝试找到拼音缩写（通常是纯小写字母的部分）
    for (const part of ywscParts) {
      const trimmedPart = part.trim();
      // 检查是否是拼音缩写（纯小写字母）
      if (/^[a-z]+$/.test(trimmedPart)) {
        const syllables = splitPinyinToSyllables(trimmedPart);
        const initials = syllables.map(s => s.charAt(0)).join('');

        return {
          original: trimmedPart,
          syllables: syllables,
          initials: initials
        };
      }
    }

    return { original: '', syllables: [], initials: '' };
  };

  /**
   * 获取药材列表
   */
  const getMedicinalItemList = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemList();
      if (errcode == 0) {
        console.log('🚀 ~ getMedicinalItemList ~ data:', data);
        // 将数据转换为MedicinalItem类型，并提取拼音字段
        const enrichedData = data.map((item: MedicinalItem) => {
          const pinyinInfo = extractPinyinFromYwsc(item.ywsc);
          return {
            ...item,
            pinyin: pinyinInfo.original, // 原始拼音字符串
            pinyinSyllables: pinyinInfo.syllables, // 拼音音节数组
            pinyinInitials: pinyinInfo.initials, // 拼音首字母缩写
          };
        });

        list.value = enrichedData;
        console.log("🚀 ~ getMedicinalItemList ~ enrichedData:", enrichedData)
        // 将数据转换为索引列表
        listLetter.value = createLetterList(enrichedData);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 创建索引列表
   * @param data 药材列表
   * @returns
   */
  const createLetterList = (data: MedicinalItem[]) => {
    // 按首字母分组
    const groupedData = {};

    data.forEach((item) => {
      // 使用已提取的拼音或药材名称的首字母
      const firstLetter = (item.pinyin && item.pinyin.charAt(0).toUpperCase()) || item.bzmc.charAt(0).toUpperCase();

      // 如果该首字母组不存在，则创建
      if (!groupedData[firstLetter]) {
        groupedData[firstLetter] = [];
      }

      // 将药材添加到对应首字母组
      groupedData[firstLetter].push(item);
    });

    // 转换为list格式
    const formattedList = Object.keys(groupedData)
      .sort()
      .map((letter) => ({
        letter,
        data: groupedData[letter],
      }));

    indexList.value = formattedList.map((item) => item.letter);
    return formattedList;
  };

  /**
   * 过滤列表
   * @param list 药材列表
   * @returns
   */
  const filterList = (list: MedicinalItem[]) => {
    const searchTerm = searchValue.value.toLowerCase().trim();

    if (!searchTerm) {
      return list;
    }

    return list.filter((item) => {
      // 匹配药材名称
      const nameMatch = item.bzmc.toLowerCase().includes(searchTerm);

      // 匹配完整拼音
      const fullPinyinMatch = item.pinyin && item.pinyin.toLowerCase().includes(searchTerm);

      // 匹配拼音首字母缩写
      const initialsMatch = item.pinyinInitials && item.pinyinInitials.toLowerCase().includes(searchTerm);

      // 匹配拼音音节数组
      let syllableMatch = false;
      if (item.pinyinSyllables && item.pinyinSyllables.length > 0) {
        // 检查是否匹配任何音节
        syllableMatch = item.pinyinSyllables.some(syllable =>
          syllable.toLowerCase().includes(searchTerm)
        );

        // 检查是否匹配音节的首字母组合
        if (!syllableMatch) {
          const syllableInitials = item.pinyinSyllables.map(s => s.charAt(0)).join('');
          syllableMatch = syllableInitials.toLowerCase().includes(searchTerm);
        }
      }

      const result = nameMatch || fullPinyinMatch || initialsMatch || syllableMatch;

      if (result) {
        console.log(`✅ 匹配成功: ${item.bzmc}`);
        console.log(`   - 拼音: ${item.pinyin}`);
        console.log(`   - 音节数组: ${JSON.stringify(item.pinyinSyllables)}`);
        console.log(`   - 首字母缩写: ${item.pinyinInitials}`);
        console.log(`   - 搜索词: ${searchTerm}`);
        console.log(`   - 匹配类型: 名称=${nameMatch}, 完整拼音=${fullPinyinMatch}, 首字母=${initialsMatch}, 音节=${syllableMatch}`);
      }

      return result;
    });
  };



  /**
   * 搜索
   */
  const handleSearch = () => {
    console.log('🚀 ~ handleSearch ~ searchValue:', searchValue.value);
    const result = filterList(list.value);
    if (result.length > 0) {
      // 过滤列表
      listLetter.value = createLetterList(result);
    } else {
      monkey.$helper.toast.warning('暂无数据');
    }
  };

  /**
   * 清空搜索
   */
  const handleClear = () => {
    listLetter.value = createLetterList(list.value);
  };

  /**
   * 点击药材项
   * @param item 药材项
   */
  const handleItemClick = (item: MedicinalItem) => {
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?ycbm=${item.ycbm}`);
  };

  onLoad(() => {
    getMedicinalItemList();
  });
</script>

<style lang="scss" scoped>
</style>
